{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=OfficersManagementDb;Trusted_Connection=true;MultipleActiveResultSets=true;Connection Timeout=30;Command Timeout=60"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Microsoft.EntityFrameworkCore.Infrastructure": "Warning"}}, "AllowedHosts": "*", "ImageSettings": {"MaxWidth": 300, "MaxHeight": 300, "MaxFileSizeKB": 80, "Quality": 60}, "ApplicationSettings": {"MaxRecordsPerPage": 50, "EnableDetailedErrors": false, "CacheExpirationMinutes": 30}}