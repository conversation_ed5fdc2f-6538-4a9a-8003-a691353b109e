using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Services;
using OfficersManagement.Models.Configuration;
using OfficersManagement.Middleware;
using System.Runtime.Versioning;

var builder = WebApplication.CreateBuilder(args);

// Configure settings
builder.Services.Configure<ImageSettings>(builder.Configuration.GetSection("ImageSettings"));
builder.Services.Configure<ApplicationSettings>(builder.Configuration.GetSection("ApplicationSettings"));

// Add services to the container.
builder.Services.AddRazorPages(options =>
{
    options.Conventions.ConfigureFilter(new Microsoft.AspNetCore.Mvc.IgnoreAntiforgeryTokenAttribute());
});
builder.Services.AddControllers();

// Add Antiforgery
builder.Services.AddAntiforgery(options =>
{
    options.HeaderName = "X-CSRF-TOKEN";
});

// Add memory caching
builder.Services.AddMemoryCache();

// Add response compression
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
});

// Add health checks
builder.Services.AddHealthChecks()
    .AddDbContextCheck<ApplicationDbContext>();

// إضافة خدمة قاعدة البيانات
builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));
    
    // Enable sensitive data logging only in development
    if (builder.Environment.IsDevelopment())
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
    
    // Configure query tracking behavior
    options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
});

// إضافة خدمة توليد أرقام الموظفين
builder.Services.AddScoped<IEmployeeNumberService, EmployeeNumberService>();

// إضافة خدمة ضغط الصور (Windows only)
if (OperatingSystem.IsWindows())
{
    builder.Services.AddScoped<ImageCompressionService>();
}
else
{
    // Add a dummy service for non-Windows platforms
    builder.Services.AddScoped<ImageCompressionService>(_ => 
        throw new PlatformNotSupportedException("Image compression is only supported on Windows"));
}

var app = builder.Build();

// Seed data
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    await context.Database.EnsureCreatedAsync();
    await SeedData.SeedProvincesAsync(context);
    await SeedData.SeedDistrictsAsync(context);
    await SeedData.SeedSubdistrictsAsync(context);
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
else
{
    app.UseDeveloperExceptionPage();
}

// Add global exception handling
app.UseMiddleware<GlobalExceptionMiddleware>();

app.UseHttpsRedirection();

// Use response compression
app.UseResponseCompression();

// Add security headers
app.Use(async (context, next) =>
{
    context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Append("X-Frame-Options", "DENY");
    context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Append("Referrer-Policy", "strict-origin-when-cross-origin");
    await next();
});

app.UseRouting();

app.UseAuthorization();

app.MapStaticAssets();
app.MapRazorPages()
   .WithStaticAssets();
app.MapControllers();

// Map health checks
app.MapHealthChecks("/health");

app.Run();
